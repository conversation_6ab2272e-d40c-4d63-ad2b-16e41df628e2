import 'package:dio/dio.dart';
import 'package:nsl/config/environment.dart';
import 'package:nsl/models/parse_validation_entity/validate_lo_model.dart';
import '../models/solution/go_model.dart';
import '../models/role_model.dart';
import '../converters/lo_text_converter.dart';
import 'base_api_service.dart';
import 'auth_service.dart';
import '../utils/logger.dart';

/// Service for handling local objective validation API operations
class LocalObjectiveValidationService extends BaseApiService {
  static final String _baseUrl = Environment.validateBaseUrl;
  static const String _validateEndpoint = '/api/workflows-lo/validate';
  static const String _mongoSaveEndpoint = '/api/workflows-lo/mongo-save';
  static const String _publishEndpoint = '/api/production/deploy';

  // Auth service for getting user data
  final AuthService _authService = AuthService();

  /// Validate local objective using the API
  /// Returns Map with 'success' boolean and 'message' string
  Future<Map<String, dynamic>> validateLocalObjective({
    required int loIndex,
    required LocalObjectivesList localObjective,
    required GlobalObjectives? globalObjectives,
    required String? functionType,
    required List<PostgresRole?> selectedRoles,
  }) async {
    try {
      Logger.info('Validating local objective LO-${loIndex + 1} via API');

      final savedData = await _authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantName;
      // Convert LO to text format using the converter
      final loText = LocalObjectiveToTextConverter.convertLocalObjectiveToText(
          localObjective,
          globalObjectives,
          null, // dataConstraints - can be null for validation
          null, // validationRules - can be null for validation
          tenantId);

      // Prepare request data with the converted text
      FormData formData = FormData.fromMap({
        'text': loText,
      });

      // Get a valid token
      final token = await _authService.getValidToken();
      if (token == null) {
        Logger.error('No valid token available for local objective validation');
        throw Exception('Authentication required');
      }

      final options = Options(
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      // Make the validation API call
      final validateUrl = '$_baseUrl$_validateEndpoint';
      Logger.info('Calling validation API: $validateUrl');

      final response = await dio.post(
        validateUrl,
        data: formData,
        options: options,
      );

      Logger.info(
          'Local objective validation response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        ValidateLoModel validateLoModel =
            ValidateLoModel.fromJson(response.data);

        if (validateLoModel.success == true) {
          Logger.info('Local objective validation successful');

          // If validation is successful, call the MongoDB save API
          final saveResult = await _saveToMongo(
            formData,
            token,
            globalObjectives: globalObjectives,
            localObjective: localObjective,
            functionType: functionType,
            selectedRoles: selectedRoles,
          );

          return {
            'success': true,
            'message': 'Local objective validated and saved successfully',
            'validation_data': response.data,
            'save_data': saveResult,
          };
        } else {
          return {
            'success': false,
            'save_data': validateLoModel,
          };
        }
      } else {
        final errorMessage =
            response.data['message'] ?? 'Failed to validate local objective';
        Logger.error('Failed to validate local objective: $errorMessage');
        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } catch (e) {
      Logger.error('Exception during local objective validation: $e');
      if (e is DioException) {
        if (e.response != null) {
          final errorMessage = e.response?.data['message'] ??
              'Local objective validation failed';
          return {
            'success': false,
            'message': errorMessage,
          };
        } else {
          return {
            'success': false,
            'message': 'Network error during local objective validation',
          };
        }
      }
      return {
        'success': false,
        'message': 'Failed to validate local objective: $e',
      };
    }
  }

  /// Save validated local objective to MongoDB
  Future<Map<String, dynamic>> _saveToMongo(
    FormData requestData,
    String token, {
    required LocalObjectivesList localObjective,
    required GlobalObjectives? globalObjectives,
    required String? functionType,
    required List<PostgresRole?> selectedRoles,
  }) async {
    try {
      Logger.info('Saving local objective to MongoDB');
      final savedData = await _authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantName;
      final options = Options(
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );
      final loText = LocalObjectiveToTextConverter.convertLocalObjectiveToText(
          localObjective,
          globalObjectives,
          null, // dataConstraints - can be null for validation
          null, // validationRules - can be null for validation
          tenantId);

      // Prepare request data with the converted text
      FormData formData = FormData.fromMap({
        'text': loText,
      });

      // Make the MongoDB save API call with the same payload
      final saveUrl = '$_baseUrl$_mongoSaveEndpoint';
      Logger.info('Calling MongoDB save API: $saveUrl');

      final response = await dio.post(
        saveUrl,
        data: formData,
        options: options,
      );

      Logger.info('MongoDB save response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        ValidateLoModel validateLoModel =
            ValidateLoModel.fromJson(response.data);
        if (validateLoModel.success == true) {
          Logger.info('Local objective saved to MongoDB successfully');
          return {
            'success': true,
            'message': 'Saved to MongoDB successfully',
            'data': validateLoModel,
          };
        } else {
          return {
            'success': false,
            'message': validateLoModel,
          };
        }
      } else {
        final errorMessage =
            response.data['message'] ?? 'Failed to save to MongoDB';
        Logger.error('Failed to save to MongoDB: $errorMessage');
        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } catch (e) {
      Logger.error('Exception during MongoDB save: $e');
      if (e is DioException) {
        if (e.response != null) {
          final errorMessage =
              e.response?.data['message'] ?? 'MongoDB save failed';
          return {
            'success': false,
            'message': errorMessage,
          };
        } else {
          return {
            'success': false,
            'message': 'Network error during MongoDB save',
          };
        }
      }
      return {
        'success': false,
        'message': 'Failed to save to MongoDB: $e',
      };
    }
  }

  Future<Map<String, dynamic>> publishLo({
    required LocalObjectivesList localObjective,
    required GlobalObjectives? globalObjectives,
    required String? functionType,
    required List<PostgresRole?> selectedRoles,
  }) async {
    try {
      Logger.info('Saving local objective to MongoDB');
      final savedData = await _authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantName;
      final token = await _authService.getValidToken();
      final options = Options(
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );
      final loText = LocalObjectiveToTextConverter.convertLocalObjectiveToText(
          localObjective,
          globalObjectives,
          null, // dataConstraints - can be null for validation
          null, // validationRules - can be null for validation
          tenantId);

      // Prepare request data with the converted text
      FormData formData = FormData.fromMap({
        'text': loText,
      });

      // Make the MongoDB save API call with the same payload
      final saveUrl = '$_baseUrl$_publishEndpoint';
      Logger.info('Calling Publish save API: $saveUrl');

      final response = await dio.post(
        saveUrl,
        data: formData,
        options: options,
      );

      Logger.info('Publish save response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        ValidateLoModel validateLoModel =
            ValidateLoModel.fromJson(response.data);
        if (validateLoModel.success == true) {
          Logger.info('Local objective saved to Publish successfully');
          return {
            'success': true,
            'message': 'Saved to Publish successfully',
            'data': validateLoModel,
          };
        } else {
          return {
            'success': false,
            'message': validateLoModel,
          };
        }
      } else {
        final errorMessage =
            response.data['message'] ?? 'Failed to save to Publish';
        Logger.error('Failed to save to Publish: $errorMessage');
        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } catch (e) {
      Logger.error('Exception during Publish save: $e');
      if (e is DioException) {
        if (e.response != null) {
          final errorMessage =
              e.response?.data['message'] ?? 'Publish save failed';
          return {
            'success': false,
            'message': errorMessage,
          };
        } else {
          return {
            'success': false,
            'message': 'Network error during Publish save',
          };
        }
      }
      return {
        'success': false,
        'message': 'Failed to save to Publish: $e',
      };
    }
  }

  @override
  Future<String?> getValidToken() async {
    return await _authService.getValidToken();
  }

  @override
  Future<String?> getUserId() async {
    return await _authService.getUserId();
  }
}
