// To parse this JSON data, do
//
//     final loginModel = loginModelFromJson(jsonString);

import 'dart:convert';

LoginModel loginModelFromJson(String str) =>
    LoginModel.fromJson(json.decode(str));

String loginModelToJson(LoginModel data) => json.encode(data.toJson());

class LoginModel {
  String? accessToken;
  String? refreshToken;
  String? tokenType;
  int? expiresAt;
  UserDetails? user;

  LoginModel({
    this.accessToken,
    this.refreshToken,
    this.tokenType,
    this.expiresAt,
    this.user,
  });

  LoginModel copyWith({
    String? accessToken,
    String? refreshToken,
    String? tokenType,
    int? expiresAt,
    UserDetails? user,
  }) =>
      LoginModel(
        accessToken: accessToken ?? this.accessToken,
        refreshToken: refreshToken ?? this.refreshToken,
        tokenType: tokenType ?? this.tokenType,
        expiresAt: expiresAt ?? this.expiresAt,
        user: user ?? this.user,
      );

  factory LoginModel.fromJson(Map<String, dynamic> json) => LoginModel(
        accessToken: json["access_token"],
        refreshToken: json["refresh_token"],
        tokenType: json["token_type"],
        expiresAt: json["expires_at"],
        user: json["user"] == null ? null : UserDetails.fromJson(json["user"]),
      );

  Map<String, dynamic> toJson() => {
        "access_token": accessToken,
        "refresh_token": refreshToken,
        "token_type": tokenType,
        "expires_at": expiresAt,
        "user": user?.toJson(),
      };
}

class UserDetails {
  String? userId;
  String? username;
  String? email;
  String? firstName;
  String? lastName;
  String? status;
  List<String>? roles;
  String? tenantId;
  String? tenantName;
  bool? disabled;
  DateTime? createdAt;

  UserDetails({
    this.userId,
    this.username,
    this.email,
    this.firstName,
    this.lastName,
    this.status,
    this.roles,
    this.tenantId,
    this.tenantName,
    this.disabled,
    this.createdAt,
  });

  UserDetails copyWith({
    String? userId,
    String? username,
    String? email,
    String? firstName,
    String? lastName,
    String? status,
    List<String>? roles,
    String? tenantId,
    String? tenantName,
    bool? disabled,
    DateTime? createdAt,
  }) =>
      UserDetails(
        userId: userId ?? this.userId,
        username: username ?? this.username,
        email: email ?? this.email,
        firstName: firstName ?? this.firstName,
        lastName: lastName ?? this.lastName,
        status: status ?? this.status,
        roles: roles ?? this.roles,
        tenantId: tenantId ?? this.tenantId,
        tenantName: tenantName ?? this.tenantName,
        disabled: disabled ?? this.disabled,
        createdAt: createdAt ?? this.createdAt,
      );

  factory UserDetails.fromJson(Map<String, dynamic> json) => UserDetails(
        userId: json["user_id"],
        username: json["username"],
        email: json["email"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        status: json["status"],
        roles: json["roles"] == null
            ? []
            : List<String>.from(json["roles"]!.map((x) => x)),
        tenantId: json["tenant_id"],
        tenantName: json["tenant_name"],
        disabled: json["disabled"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
      );

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "username": username,
        "email": email,
        "first_name": firstName,
        "last_name": lastName,
        "status": status,
        "roles": roles == null ? [] : List<dynamic>.from(roles!.map((x) => x)),
        "tenant_id": tenantId,
        "tenant_name": tenantName,
        "disabled": disabled,
        "created_at": createdAt?.toIso8601String(),
      };
}
