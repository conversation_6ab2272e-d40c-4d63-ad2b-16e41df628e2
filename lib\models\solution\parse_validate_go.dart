import 'dart:convert';
import 'go_model.dart';

// Root Response Class
class ParseValidateGo {
  bool? success;
  List<String>? messages;
  ParsedData? parsedData;
  dynamic validationErrors;
  dynamic errorDetails;
  Map<String, dynamic>? parsedGos;
  Map<String, dynamic>? parsedLos;
  bool? isValid;

  ParseValidateGo({
    this.success,
    this.messages,
    this.parsedData,
    this.validationErrors,
    this.errorDetails,
    this.parsedGos,
    this.parsedLos,
    this.isValid,
  });

  factory ParseValidateGo.fromJson(Map<String, dynamic> json) {
    return ParseValidateGo(
      success: json['success'],
      messages: json['messages']?.cast<String>(),
      parsedData: json['parsed_data'] != null
          ? ParsedData.fromJson(json['parsed_data'])
          : null,
      validationErrors: json['validation_errors'],
      errorDetails: json['error_details'],
      parsedGos: json['parsed_gos'],
      parsedLos: json['parsed_los'],
      isValid: json['is_valid'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'messages': messages,
      'parsed_data': parsedData?.toJson(),
      'validation_errors': validationErrors,
      'error_details': errorDetails,
      'parsed_gos': parsedGos,
      'parsed_los': parsedLos,
      'is_valid': isValid,
    };
  }
}

// Parsed Data Class
class ParsedData {
  GoData? go;

  ParsedData({this.go});

  factory ParsedData.fromJson(Map<String, dynamic> json) {
    return ParsedData(
      go: json['GO'] != null ? GoData.fromJson(json['GO']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'GO': go?.toJson(),
    };
  }
}

// Enhanced GoData Class (contains all GO information)
class GoData {
  EnhancedGlobalObjectives? globalObjectives;
  EnhancedProcessOwnership? processOwnership;
  EnhancedTriggerDefinition? triggerDefinition;
  List<EnhancedLocalObjectivesList>? localObjectivesList;
  List<EnhancedPathwayDefinition>? pathwayDefinitions;
  EnhancedPerformanceMetadata? performanceMetadata;
  EnhancedProcessMiningSchema? processMiningSchema;
  EnhancedPerformanceDiscoveryMetrics? performanceDiscoveryMetrics;
  EnhancedConformanceAnalytics? conformanceAnalytics;
  EnhancedAdvancedProcessIntelligence? advancedProcessIntelligence;
  List<EnhancedRollbackPathway>? rollbackPathways;
  EnhancedValidationRules? validationRules;
  List<EnhancedDataConstraint>? dataConstraints;
  List<EnhancedProcessFlow>? processFlow;

  GoData({
    this.globalObjectives,
    this.processOwnership,
    this.triggerDefinition,
    this.localObjectivesList,
    this.pathwayDefinitions,
    this.performanceMetadata,
    this.processMiningSchema,
    this.performanceDiscoveryMetrics,
    this.conformanceAnalytics,
    this.advancedProcessIntelligence,
    this.rollbackPathways,
    this.validationRules,
    this.dataConstraints,
    this.processFlow,
  });

  factory GoData.fromJson(Map<String, dynamic> json) {
    return GoData(
      globalObjectives: json['global_objectives'] != null
          ? EnhancedGlobalObjectives.fromJson(json['global_objectives'])
          : null,
      processOwnership: json['process_ownership'] != null
          ? EnhancedProcessOwnership.fromJson(json['process_ownership'])
          : null,
      triggerDefinition: json['trigger_definition'] != null
          ? EnhancedTriggerDefinition.fromJson(json['trigger_definition'])
          : null,
      localObjectivesList: json['local_objectives_list'] != null
          ? (json['local_objectives_list'] as List)
          .map((lo) => EnhancedLocalObjectivesList.fromJson(lo))
          .toList()
          : null,
      pathwayDefinitions: json['pathway_definitions'] != null
          ? (json['pathway_definitions'] as List)
          .map((pd) => EnhancedPathwayDefinition.fromJson(pd))
          .toList()
          : null,
      performanceMetadata: json['performance_metadata'] != null
          ? EnhancedPerformanceMetadata.fromJson(json['performance_metadata'])
          : null,
      processMiningSchema: json['process_mining_schema'] != null
          ? EnhancedProcessMiningSchema.fromJson(json['process_mining_schema'])
          : null,
      performanceDiscoveryMetrics: json['performance_discovery_metrics'] != null
          ? EnhancedPerformanceDiscoveryMetrics.fromJson(json['performance_discovery_metrics'])
          : null,
      conformanceAnalytics: json['conformance_analytics'] != null
          ? EnhancedConformanceAnalytics.fromJson(json['conformance_analytics'])
          : null,
      advancedProcessIntelligence: json['advanced_process_intelligence'] != null
          ? EnhancedAdvancedProcessIntelligence.fromJson(json['advanced_process_intelligence'])
          : null,
      rollbackPathways: json['rollback_pathways'] != null
          ? (json['rollback_pathways'] as List)
          .map((rp) => EnhancedRollbackPathway.fromJson(rp))
          .toList()
          : null,
      validationRules: json['validation_rules'] != null
          ? EnhancedValidationRules.fromJson(json['validation_rules'])
          : null,
      dataConstraints: json['data_constraints'] != null
          ? (json['data_constraints'] as List)
          .map((dc) => EnhancedDataConstraint.fromJson(dc))
          .toList()
          : null,
      processFlow: json['process_flow'] != null
          ? (json['process_flow'] as List)
          .map((pf) => EnhancedProcessFlow.fromJson(pf))
          .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'global_objectives': globalObjectives?.toJson(),
      'process_ownership': processOwnership?.toJson(),
      'trigger_definition': triggerDefinition?.toJson(),
      'local_objectives_list': localObjectivesList?.map((lo) => lo.toJson()).toList(),
      'pathway_definitions': pathwayDefinitions?.map((pd) => pd.toJson()).toList(),
      'performance_metadata': performanceMetadata?.toJson(),
      'process_mining_schema': processMiningSchema?.toJson(),
      'performance_discovery_metrics': performanceDiscoveryMetrics?.toJson(),
      'conformance_analytics': conformanceAnalytics?.toJson(),
      'advanced_process_intelligence': advancedProcessIntelligence?.toJson(),
      'rollback_pathways': rollbackPathways?.map((rp) => rp.toJson()).toList(),
      'validation_rules': validationRules?.toJson(),
      'data_constraints': dataConstraints?.map((dc) => dc.toJson()).toList(),
      'process_flow': processFlow?.map((pf) => pf.toJson()).toList(),
    };
  }
}

// Enhanced Global Objectives
class EnhancedGlobalObjectives extends GlobalObjectives {
  EnhancedGlobalObjectives({
    super.naturalLanguage,
    super.name,
    super.version,
    super.status,
    super.description,
    super.primaryEntity,
    super.classification,
    super.agentType,
    super.bookName,
    super.chapterName,
    super.tenantName,
    super.goId,
    super.tenantId,
    super.bookId,
    super.chapterId,
    super.versionType,
    super.roleType,
  });

  factory EnhancedGlobalObjectives.fromJson(Map<String, dynamic> json) {
    return EnhancedGlobalObjectives(
      naturalLanguage: json['natural_language'],
      name: json['name'],
      version: json['version'],
      status: json['status'],
      description: json['description'],
      primaryEntity: json['primary_entity'],
      classification: json['classification'],
      agentType: json['agent_type'],
      bookName: json['book_name'],
      chapterName: json['chapter_name'],
      tenantName: json['tenant_name'],
      goId: json['go_id'],
      tenantId: json['tenant_id'],
      bookId: json['book_id'],
      chapterId: json['chapter_id'],
      versionType: json['version_type'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'natural_language': naturalLanguage,
      'name': name,
      'version': version,
      'status': status,
      'description': description,
      'primary_entity': primaryEntity,
      'classification': classification,
      'agent_type': agentType,
      'book_name': bookName,
      'chapter_name': chapterName,
      'tenant_name': tenantName,
      'go_id': goId,
      'tenant_id': tenantId,
      'book_id': bookId,
      'chapter_id': chapterId,
      'version_type': versionType,
    };
  }
}

// Enhanced Process Ownership
class EnhancedProcessOwnership extends ProcessOwnership {
  EnhancedProcessOwnership({
    super.naturalLanguage,
    super.originator,
    super.processOwner,
    super.businessSponsor,
    super.id,
    super.goId,
  });

  factory EnhancedProcessOwnership.fromJson(Map<String, dynamic> json) {
    return EnhancedProcessOwnership(
      naturalLanguage: json['natural_language'],
      originator: json['originator'],
      processOwner: json['process_owner'],
      businessSponsor: json['business_sponsor'],
      id: json['id'],
      goId: json['go_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'natural_language': naturalLanguage,
      'originator': originator,
      'process_owner': processOwner,
      'business_sponsor': businessSponsor,
      'id': id,
      'go_id': goId,
    };
  }
}

// Enhanced Trigger Definition
class EnhancedTriggerDefinition extends TriggerDefinition {
  EnhancedTriggerDefinition({
    super.naturalLanguage,
    super.triggerType,
    super.triggerCondition,
    super.triggerSchedule,
    super.triggerAttributes,
    super.id,
    super.goId,
  });

  factory EnhancedTriggerDefinition.fromJson(Map<String, dynamic> json) {
    return EnhancedTriggerDefinition(
      naturalLanguage: json['natural_language'],
      triggerType: json['trigger_type'],
      triggerCondition: json['trigger_condition'],
      triggerSchedule: json['trigger_schedule'],
      triggerAttributes: json['trigger_attributes']?.cast<String>(),
      id: json['id'],
      goId: json['go_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'natural_language': naturalLanguage,
      'trigger_type': triggerType,
      'trigger_condition': triggerCondition,
      'trigger_schedule': triggerSchedule,
      'trigger_attributes': triggerAttributes,
      'id': id,
      'go_id': goId,
    };
  }
}

// Enhanced Local Objectives List
class EnhancedLocalObjectivesList extends LocalObjectivesList {
  String? loName;
  String? actorType;
  String? id;

  EnhancedLocalObjectivesList({
    super.loNumber,
    super.workSource,
    super.terminal,
    super.name,
    super.version,
    super.status,
    super.workflowSource,
    super.functionType,
    super.agentType,
    super.executionRights,
    super.tenantName,
    super.tenantId,
    super.uiType,
    super.naturalLanguage,
    super.goId,
    super.loId,
    super.entitiesList,
    super.roleType,
    super.pathwayData,
    this.loName,
    this.actorType,
    this.id,
  });

  factory EnhancedLocalObjectivesList.fromJson(Map<String, dynamic> json) {
    return EnhancedLocalObjectivesList(
      loNumber: json['lo_number'],
      loName: json['lo_name'],
      actorType: json['actor_type'],
      naturalLanguage: json['natural_language'],
      id: json['id'],
      workSource: json['work_source'],
      terminal: json['terminal'],
      name: json['lo_name'],
      agentType: json['actor_type'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'lo_number': loNumber,
      'lo_name': loName,
      'actor_type': actorType,
      'natural_language': naturalLanguage,
      'id': id,
      'work_source': workSource,
      'terminal': terminal,
    };
  }
}

// Enhanced Pathway Definition
class EnhancedPathwayDefinition extends PathwayDefinition {
  EnhancedPathwayDefinition({
    super.id,
    super.pathwayNumber,
    super.pathwayName,
    super.steps,
    super.naturalLanguage,
  });

  factory EnhancedPathwayDefinition.fromJson(Map<String, dynamic> json) {
    return EnhancedPathwayDefinition(
      id: json['id'],
      pathwayNumber: json['pathway_number'],
      pathwayName: json['pathway_name'],
      steps: json['steps']?.cast<String>(),
      naturalLanguage: json['natural_language'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'pathway_number': pathwayNumber,
      'pathway_name': pathwayName,
      'steps': steps,
      'natural_language': naturalLanguage,
    };
  }
}

// Enhanced Performance Metadata
class EnhancedPerformanceMetadata {
  String? naturalLanguage;
  EnhancedMetadataData? metadataData;
  String? id;
  String? goId;

  EnhancedPerformanceMetadata({
    this.naturalLanguage,
    this.metadataData,
    this.id,
    this.goId,
  });

  factory EnhancedPerformanceMetadata.fromJson(Map<String, dynamic> json) {
    return EnhancedPerformanceMetadata(
      naturalLanguage: json['natural_language'],
      metadataData: json['metadata_data'] != null
          ? EnhancedMetadataData.fromJson(json['metadata_data'])
          : null,
      id: json['id'],
      goId: json['go_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'natural_language': naturalLanguage,
      'metadata_data': metadataData?.toJson(),
      'id': id,
      'go_id': goId,
    };
  }
}

// Enhanced Metadata Data
class EnhancedMetadataData extends MetadataData {
  EnhancedMetadataData({
    super.cycleTime,
    super.numberOfPathways,
    super.volumeMetrics,
    super.slaThresholds,
    super.criticalLoPerformance,
  });

  factory EnhancedMetadataData.fromJson(Map<String, dynamic> json) {
    return EnhancedMetadataData(
      cycleTime: json['cycle_time'],
      numberOfPathways: json['number_of_pathways'],
      volumeMetrics: json['volume_metrics'] != null
          ? EnhancedVolumeMetrics.fromJson(json['volume_metrics'])
          : null,
      slaThresholds: json['sla_thresholds'] != null
          ? EnhancedSlaThresholds.fromJson(json['sla_thresholds'])
          : null,
      criticalLoPerformance: json['critical_lo_performance'] != null
          ? EnhancedCriticalLoPerformance.fromJson(json['critical_lo_performance'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'cycle_time': cycleTime,
      'number_of_pathways': numberOfPathways,
      'volume_metrics': (volumeMetrics as EnhancedVolumeMetrics?)?.toJson(),
      'sla_thresholds': (slaThresholds as EnhancedSlaThresholds?)?.toJson(),
      'critical_lo_performance': (criticalLoPerformance as EnhancedCriticalLoPerformance?)?.toJson(),
    };
  }
}

// Enhanced Volume Metrics
class EnhancedVolumeMetrics extends VolumeMetrics {
  EnhancedVolumeMetrics({
    super.averageVolume,
    super.peakVolume,
    super.unit,
  });

  factory EnhancedVolumeMetrics.fromJson(Map<String, dynamic> json) {
    return EnhancedVolumeMetrics(
      averageVolume: json['average_volume'],
      peakVolume: json['peak_volume'],
      unit: json['unit'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'average_volume': averageVolume,
      'peak_volume': peakVolume,
      'unit': unit,
    };
  }
}

// Enhanced SLA Thresholds
class EnhancedSlaThresholds extends SlaThresholds {
  EnhancedSlaThresholds({
    super.recordCreation,
    super.recordUpdate,
    super.recordSearch,
    super.recordDeletion,
  });

  factory EnhancedSlaThresholds.fromJson(Map<String, dynamic> json) {
    return EnhancedSlaThresholds(
      recordCreation: json['record_creation'],
      recordUpdate: json['record_update'],
      recordSearch: json['record_search'],
      recordDeletion: json['record_deletion'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'record_creation': recordCreation,
      'record_update': recordUpdate,
      'record_search': recordSearch,
      'record_deletion': recordDeletion,
    };
  }
}

// Enhanced Critical LO Performance
class EnhancedCriticalLoPerformance extends CriticalLoPerformance {
  EnhancedCriticalLoPerformance({
    super.createEmployeeRecord,
    super.updateEmployeeRecord,
    super.viewEmployeeDatabase,
    super.deleteEmployeeRecord,
  });

  factory EnhancedCriticalLoPerformance.fromJson(Map<String, dynamic> json) {
    return EnhancedCriticalLoPerformance(
      createEmployeeRecord: json['CreateEmployeeRecord'],
      updateEmployeeRecord: json['UpdateEmployeeRecord'],
      viewEmployeeDatabase: json['ViewEmployeeDatabase'],
      deleteEmployeeRecord: json['DeleteEmployeeRecord'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'CreateEmployeeRecord': createEmployeeRecord,
      'UpdateEmployeeRecord': updateEmployeeRecord,
      'ViewEmployeeDatabase': viewEmployeeDatabase,
      'DeleteEmployeeRecord': deleteEmployeeRecord,
    };
  }
}

// Enhanced Process Mining Schema
class EnhancedProcessMiningSchema extends ProcessMiningSchema {
  EnhancedProcessMiningSchema({
    super.naturalLanguage,
    super.schemaData,
    super.performanceDiscoveryMetrics,
    super.conformanceAnalytics,
    super.advancedProcessIntelligence,
    super.id,
    super.goId,
  });

  factory EnhancedProcessMiningSchema.fromJson(Map<String, dynamic> json) {
    return EnhancedProcessMiningSchema(
      naturalLanguage: json['natural_language'],
      id: json['id'],
      goId: json['go_id'],
      schemaData: json['schema_data'] != null
          ? EnhancedSchemaData.fromJson(json['schema_data'])
          : null,
      // performanceDiscoveryMetrics: json['performance_discovery_metrics'] != null
      //     ? EnhancedProcessMiningPerformanceDiscoveryMetrics.fromJson(json['performance_discovery_metrics'])
      //     : null,
      // conformanceAnalytics: json['conformance_analytics'] != null
      //     ? EnhancedProcessMiningConformanceAnalytics.fromJson(json['conformance_analytics'])
      //     : null,
      // advancedProcessIntelligence: json['advanced_process_intelligence'] != null
      //     ? EnhancedProcessMiningAdvancedProcessIntelligence.fromJson(json['advanced_process_intelligence'])
      //     : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'natural_language': naturalLanguage,
      'id': id,
      'go_id': goId,
      'schema_data': (schemaData as EnhancedSchemaData?)?.toJson(),
      'performance_discovery_metrics': (performanceDiscoveryMetrics as EnhancedProcessMiningPerformanceDiscoveryMetrics?)?.toJson(),
      'conformance_analytics': (conformanceAnalytics as EnhancedProcessMiningConformanceAnalytics?)?.toJson(),
      'advanced_process_intelligence': (advancedProcessIntelligence as EnhancedProcessMiningAdvancedProcessIntelligence?)?.toJson(),
    };
  }
}

// Enhanced Schema Data
class EnhancedSchemaData extends SchemaData {
  EnhancedSchemaData({
    super.eventLogSpecification,
  });

  factory EnhancedSchemaData.fromJson(Map<String, dynamic> json) {
    return EnhancedSchemaData(
      eventLogSpecification: json['event_log_specification'] != null
          ? EnhancedEventLogSpecification.fromJson(json['event_log_specification'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'event_log_specification': (eventLogSpecification as EnhancedEventLogSpecification?)?.toJson(),
    };
  }
}

// Enhanced Event Log Specification
class EnhancedEventLogSpecification extends EventLogSpecification {
  EnhancedEventLogSpecification({
    super.caseId,
    super.activity,
    super.eventType,
    super.timestamp,
    super.resource,
    super.duration,
    super.attributes,
  });

  factory EnhancedEventLogSpecification.fromJson(Map<String, dynamic> json) {
    return EnhancedEventLogSpecification(
      caseId: json['case_id'],
      activity: json['activity'],
      eventType: json['event_type'],
      timestamp: json['timestamp'],
      resource: json['resource'],
      duration: json['duration'],
      attributes: json['attributes'] != null
          ? EnhancedAttributes.fromJson(json['attributes'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'case_id': caseId,
      'activity': activity,
      'event_type': eventType,
      'timestamp': timestamp,
      'resource': resource,
      'duration': duration,
      'attributes': (attributes as EnhancedAttributes?)?.toJson(),
    };
  }
}

// Enhanced Attributes
class EnhancedAttributes extends Attributes {
  EnhancedAttributes({
    super.entityState,
    super.inputValues,
    super.outputValues,
    super.executionStatus,
    super.errorDetails,
  });

  factory EnhancedAttributes.fromJson(Map<String, dynamic> json) {
    return EnhancedAttributes(
      entityState: json['entity_state'],
      inputValues: json['input_values'],
      outputValues: json['output_values'],
      executionStatus: json['execution_status'],
      errorDetails: json['error_details'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'entity_state': entityState,
      'input_values': inputValues,
      'output_values': outputValues,
      'execution_status': executionStatus,
      'error_details': errorDetails,
    };
  }
}

// Additional classes for Performance Discovery Metrics, Conformance Analytics, etc.
class EnhancedProcessMiningPerformanceDiscoveryMetrics {
  Map<String, dynamic>? pathwayFrequency;
  Map<String, dynamic>? bottleneckAnalysis;
  Map<String, dynamic>? resourcePatterns;

  EnhancedProcessMiningPerformanceDiscoveryMetrics({
    this.pathwayFrequency,
    this.bottleneckAnalysis,
    this.resourcePatterns,
  });

  factory EnhancedProcessMiningPerformanceDiscoveryMetrics.fromJson(Map<String, dynamic> json) {
    return EnhancedProcessMiningPerformanceDiscoveryMetrics(
      pathwayFrequency: json['pathway_frequency'],
      bottleneckAnalysis: json['bottleneck_analysis'],
      resourcePatterns: json['resource_patterns'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pathway_frequency': pathwayFrequency,
      'bottleneck_analysis': bottleneckAnalysis,
      'resource_patterns': resourcePatterns,
    };
  }
}

class EnhancedProcessMiningConformanceAnalytics {
  int? complianceRate;
  Map<String, dynamic>? executionVariance;
  Map<String, dynamic>? exceptionPatterns;

  EnhancedProcessMiningConformanceAnalytics({
    this.complianceRate,
    this.executionVariance,
    this.exceptionPatterns,
  });

  factory EnhancedProcessMiningConformanceAnalytics.fromJson(Map<String, dynamic> json) {
    return EnhancedProcessMiningConformanceAnalytics(
      complianceRate: json['compliance_rate'],
      executionVariance: json['execution_variance'],
      exceptionPatterns: json['exception_patterns'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'compliance_rate': complianceRate,
      'execution_variance': executionVariance,
      'exception_patterns': exceptionPatterns,
    };
  }
}

class EnhancedProcessMiningAdvancedProcessIntelligence {
  EnhancedProcessHealthScore? processHealthScore;
  EnhancedOptimizationInsights? optimizationInsights;

  EnhancedProcessMiningAdvancedProcessIntelligence({
    this.processHealthScore,
    this.optimizationInsights,
  });

  factory EnhancedProcessMiningAdvancedProcessIntelligence.fromJson(Map<String, dynamic> json) {
    return EnhancedProcessMiningAdvancedProcessIntelligence(
      processHealthScore: json['process_health_score'] != null
          ? EnhancedProcessHealthScore.fromJson(json['process_health_score'])
          : null,
      optimizationInsights: json['optimization_insights'] != null
          ? EnhancedOptimizationInsights.fromJson(json['optimization_insights'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'process_health_score': processHealthScore?.toJson(),
      'optimization_insights': optimizationInsights?.toJson(),
    };
  }
}

class EnhancedProcessHealthScore extends ProcessHealthScore {
  EnhancedProcessHealthScore({
    super.performanceScore,
    super.complianceScore,
    super.efficiencyScore,
    super.overallHealth,
  });

  factory EnhancedProcessHealthScore.fromJson(Map<String, dynamic> json) {
    return EnhancedProcessHealthScore(
      performanceScore: json['performance_score'],
      complianceScore: json['compliance_score'],
      efficiencyScore: json['efficiency_score'],
      overallHealth: json['overall_health'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'performance_score': performanceScore,
      'compliance_score': complianceScore,
      'efficiency_score': efficiencyScore,
      'overall_health': overallHealth,
    };
  }
}

class EnhancedOptimizationInsights extends OptimizationInsights {
  EnhancedOptimizationInsights({
    super.bottleneckElimination,
    super.resourceReallocation,
    super.pathwayOptimization,
  });

  factory EnhancedOptimizationInsights.fromJson(Map<String, dynamic> json) {
    return EnhancedOptimizationInsights(
      bottleneckElimination: json['bottleneck_elimination']?.cast<String>(),
      resourceReallocation: json['resource_reallocation']?.cast<String>(),
      pathwayOptimization: json['pathway_optimization']?.cast<String>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'bottleneck_elimination': bottleneckElimination,
      'resource_reallocation': resourceReallocation,
      'pathway_optimization': pathwayOptimization,
    };
  }
}

// Performance Discovery Metrics
class EnhancedPerformanceDiscoveryMetrics {
  String? id;
  String? goId;
  String? naturalLanguage;
  Map<String, EnhancedResourcePattern>? resourcePatterns;

  EnhancedPerformanceDiscoveryMetrics({
    this.id,
    this.goId,
    this.naturalLanguage,
    this.resourcePatterns,
  });

  factory EnhancedPerformanceDiscoveryMetrics.fromJson(Map<String, dynamic> json) {
    Map<String, EnhancedResourcePattern>? patterns;
    if (json['resource_patterns'] != null) {
      patterns = {};
      (json['resource_patterns'] as Map<String, dynamic>).forEach((key, value) {
        patterns![key] = EnhancedResourcePattern.fromJson(value);
      });
    }

    return EnhancedPerformanceDiscoveryMetrics(
      id: json['id'],
      goId: json['go_id'],
      naturalLanguage: json['natural_language'],
      resourcePatterns: patterns,
    );
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic>? patternsJson;
    if (resourcePatterns != null) {
      patternsJson = {};
      resourcePatterns!.forEach((key, value) {
        patternsJson![key] = value.toJson();
      });
    }

    return {
      'id': id,
      'go_id': goId,
      'natural_language': naturalLanguage,
      'resource_patterns': patternsJson,
    };
  }
}

// Enhanced Resource Pattern
class EnhancedResourcePattern {
  String? activeHours;
  String? peakLoadPeriods;
  int? concurrentExecutions;

  EnhancedResourcePattern({
    this.activeHours,
    this.peakLoadPeriods,
    this.concurrentExecutions,
  });

  factory EnhancedResourcePattern.fromJson(Map<String, dynamic> json) {
    return EnhancedResourcePattern(
      activeHours: json['active_hours'],
      peakLoadPeriods: json['peak_load_periods'],
      concurrentExecutions: json['concurrent_executions'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'active_hours': activeHours,
      'peak_load_periods': peakLoadPeriods,
      'concurrent_executions': concurrentExecutions,
    };
  }
}

// Enhanced Conformance Analytics
class EnhancedConformanceAnalytics {
  String? id;
  String? goId;
  String? naturalLanguage;
  int? complianceRate;
  EnhancedExceptionPatterns? exceptionPatterns;

  EnhancedConformanceAnalytics({
    this.id,
    this.goId,
    this.naturalLanguage,
    this.complianceRate,
    this.exceptionPatterns,
  });

  factory EnhancedConformanceAnalytics.fromJson(Map<String, dynamic> json) {
    return EnhancedConformanceAnalytics(
      id: json['id'],
      goId: json['go_id'],
      naturalLanguage: json['natural_language'],
      complianceRate: json['compliance_rate'],
      exceptionPatterns: json['exception_patterns'] != null
          ? EnhancedExceptionPatterns.fromJson(json['exception_patterns'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'go_id': goId,
      'natural_language': naturalLanguage,
      'compliance_rate': complianceRate,
      'exception_patterns': exceptionPatterns?.toJson(),
    };
  }
}

// Enhanced Exception Patterns
class EnhancedExceptionPatterns {
  EnhancedValidationFailure? validationFailure;
  EnhancedSystemError? systemError;

  EnhancedExceptionPatterns({
    this.validationFailure,
    this.systemError,
  });

  factory EnhancedExceptionPatterns.fromJson(Map<String, dynamic> json) {
    return EnhancedExceptionPatterns(
      validationFailure: json['validation_failure'] != null
          ? EnhancedValidationFailure.fromJson(json['validation_failure'])
          : null,
      systemError: json['system_error'] != null
          ? EnhancedSystemError.fromJson(json['system_error'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'validation_failure': validationFailure?.toJson(),
      'system_error': systemError?.toJson(),
    };
  }
}

// Enhanced Validation Failure
class EnhancedValidationFailure {
  int? frequency;
  List<String>? mostCommonFailures;
  String? resolutionTime;

  EnhancedValidationFailure({
    this.frequency,
    this.mostCommonFailures,
    this.resolutionTime,
  });

  factory EnhancedValidationFailure.fromJson(Map<String, dynamic> json) {
    return EnhancedValidationFailure(
      frequency: json['frequency'],
      mostCommonFailures: json['most_common_failures']?.cast<String>(),
      resolutionTime: json['resolution_time'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'frequency': frequency,
      'most_common_failures': mostCommonFailures,
      'resolution_time': resolutionTime,
    };
  }
}

// Enhanced System Error
class EnhancedSystemError {
  int? frequency;
  List<String>? errorCategories;
  int? automaticRecoveryRate;

  EnhancedSystemError({
    this.frequency,
    this.errorCategories,
    this.automaticRecoveryRate,
  });

  factory EnhancedSystemError.fromJson(Map<String, dynamic> json) {
    return EnhancedSystemError(
      frequency: json['frequency'],
      errorCategories: json['error_categories']?.cast<String>(),
      automaticRecoveryRate: json['automatic_recovery_rate'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'frequency': frequency,
      'error_categories': errorCategories,
      'automatic_recovery_rate': automaticRecoveryRate,
    };
  }
}

// Enhanced Advanced Process Intelligence
class EnhancedAdvancedProcessIntelligence {
  String? id;
  String? goId;
  String? naturalLanguage;
  EnhancedProcessHealthScore? processHealthScore;
  EnhancedPredictionModels? predictionModels;
  EnhancedOptimizationInsights? optimizationInsights;

  EnhancedAdvancedProcessIntelligence({
    this.id,
    this.goId,
    this.naturalLanguage,
    this.processHealthScore,
    this.predictionModels,
    this.optimizationInsights,
  });

  factory EnhancedAdvancedProcessIntelligence.fromJson(Map<String, dynamic> json) {
    return EnhancedAdvancedProcessIntelligence(
      id: json['id'],
      goId: json['go_id'],
      naturalLanguage: json['natural_language'],
      processHealthScore: json['process_health_score'] != null
          ? EnhancedProcessHealthScore.fromJson(json['process_health_score'])
          : null,
      predictionModels: json['prediction_models'] != null
          ? EnhancedPredictionModels.fromJson(json['prediction_models'])
          : null,
      optimizationInsights: json['optimization_insights'] != null
          ? EnhancedOptimizationInsights.fromJson(json['optimization_insights'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'go_id': goId,
      'natural_language': naturalLanguage,
      'process_health_score': processHealthScore?.toJson(),
      'prediction_models': predictionModels?.toJson(),
      'optimization_insights': optimizationInsights?.toJson(),
    };
  }
}

// Enhanced Prediction Models
class EnhancedPredictionModels {
  EnhancedCompletionTimeForecast? completionTimeForecast;

  EnhancedPredictionModels({
    this.completionTimeForecast,
  });

  factory EnhancedPredictionModels.fromJson(Map<String, dynamic> json) {
    return EnhancedPredictionModels(
      completionTimeForecast: json['completion_time_forecast'] != null
          ? EnhancedCompletionTimeForecast.fromJson(json['completion_time_forecast'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'completion_time_forecast': completionTimeForecast?.toJson(),
    };
  }
}

// Enhanced Completion Time Forecast
class EnhancedCompletionTimeForecast extends CompletionTimeForecast {
  EnhancedCompletionTimeForecast({
    super.algorithm,
    super.accuracy,
    super.confidenceInterval,
  });

  factory EnhancedCompletionTimeForecast.fromJson(Map<String, dynamic> json) {
    return EnhancedCompletionTimeForecast(
      algorithm: json['algorithm'],
      accuracy: json['accuracy'],
      confidenceInterval: json['confidence_interval'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'algorithm': algorithm,
      'accuracy': accuracy,
      'confidence_interval': confidenceInterval,
    };
  }
}

// Enhanced Rollback Pathway
class EnhancedRollbackPathway extends RollbackPathway {
  EnhancedRollbackPathway({
    super.id,
    super.goId,
    super.fromLo,
    super.toLo,
    super.pathwayType,
    super.naturalLanguage,
  });

  factory EnhancedRollbackPathway.fromJson(Map<String, dynamic> json) {
    return EnhancedRollbackPathway(
      id: json['id'],
      goId: json['go_id'],
      fromLo: json['from_lo'],
      toLo: json['to_lo'],
      pathwayType: json['pathway_type'],
      naturalLanguage: json['natural_language'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'go_id': goId,
      'from_lo': fromLo,
      'to_lo': toLo,
      'pathway_type': pathwayType,
      'natural_language': naturalLanguage,
    };
  }
}

// Enhanced Validation Rules
class EnhancedValidationRules {
  String? id;
  String? goId;
  List<EnhancedRule>? rules;
  String? naturalLanguage;

  EnhancedValidationRules({
    this.id,
    this.goId,
    this.rules,
    this.naturalLanguage,
  });

  factory EnhancedValidationRules.fromJson(Map<String, dynamic> json) {
    return EnhancedValidationRules(
      id: json['id'],
      goId: json['go_id'],
      naturalLanguage: json['natural_language'],
      rules: json['rules'] != null
          ? (json['rules'] as List)
          .map((rule) => EnhancedRule.fromJson(rule))
          .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'go_id': goId,
      'natural_language': naturalLanguage,
      'rules': rules?.map((rule) => rule.toJson()).toList(),
    };
  }
}

// Enhanced Rule
class EnhancedRule extends Rule {
  EnhancedRule({
    super.id,
    super.ruleName,
    super.naturalLanguage,
    super.ruleInputs,
    super.ruleOperation,
    super.ruleDescription,
    super.ruleOutput,
    super.ruleError,
    super.ruleValidation,
  });

  factory EnhancedRule.fromJson(Map<String, dynamic> json) {
    return EnhancedRule(
      id: json['id'],
      ruleName: json['rule_name'],
      naturalLanguage: json['natural_language'],
      ruleInputs: json['rule_inputs']?.cast<String>(),
      ruleOperation: json['rule_operation'],
      ruleDescription: json['rule_description'],
      ruleOutput: json['rule_output'],
      ruleError: json['rule_error'],
      ruleValidation: json['rule_validation'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'rule_name': ruleName,
      'natural_language': naturalLanguage,
      'rule_inputs': ruleInputs,
      'rule_operation': ruleOperation,
      'rule_description': ruleDescription,
      'rule_output': ruleOutput,
      'rule_error': ruleError,
      'rule_validation': ruleValidation,
    };
  }
}

// Enhanced Data Constraint
class EnhancedDataConstraint extends DataConstraint {
  EnhancedDataConstraint({
    super.id,
    super.goId,
    super.entity,
    super.attribute,
    super.dataType,
    super.constraintType,
    super.constraintText,
    super.errorMessage,
    super.naturalLanguage,
  });

  factory EnhancedDataConstraint.fromJson(Map<String, dynamic> json) {
    return EnhancedDataConstraint(
      id: json['id'],
      goId: json['go_id'],
      entity: json['entity'],
      attribute: json['attribute'],
      dataType: json['data_type'],
      constraintType: json['constraint_type'],
      constraintText: json['constraint_text'],
      errorMessage: json['error_message'],
      naturalLanguage: json['natural_language'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'go_id': goId,
      'entity': entity,
      'attribute': attribute,
      'data_type': dataType,
      'constraint_type': constraintType,
      'constraint_text': constraintText,
      'error_message': errorMessage,
      'natural_language': naturalLanguage,
    };
  }
}

// Enhanced Process Flow
class EnhancedProcessFlow extends ProcessFlow {
  EnhancedProcessFlow({
    super.loName,
    super.actorType,
    super.description,
    super.routeType,
    super.id,
    super.goId,
    super.loId,
    super.naturalLanguage,
    super.routes,
  });

  factory EnhancedProcessFlow.fromJson(Map<String, dynamic> json) {
    return EnhancedProcessFlow(
      loName: json['lo_name'],
      actorType: json['actor_type'],
      description: json['description'],
      routeType: json['route_type'],
      id: json['id'],
      goId: json['go_id'],
      loId: json['lo_id'],
      naturalLanguage: json['natural_language'],
      routes: json['routes']?.cast<String>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'lo_name': loName,
      'actor_type': actorType,
      'description': description,
      'route_type': routeType,
      'id': id,
      'go_id': goId,
      'lo_id': loId,
      'natural_language': naturalLanguage,
      'routes': routes,
    };
  }
}

// Main JSON Converter Class
class JsonPojoConverter {
  // Convert JSON string to ApiResponse
  static ParseValidateGo fromJsonString(String jsonString) {
    final Map<String, dynamic> json = jsonDecode(jsonString);
    return ParseValidateGo.fromJson(json);
  }

  // Convert JSON Map to ApiResponse
  static ParseValidateGo fromJsonMap(Map<String, dynamic> json) {
    return ParseValidateGo.fromJson(json);
  }

  // Convert ApiResponse to JSON string
  static String toJsonString(ParseValidateGo response) {
    return jsonEncode(response.toJson());
  }

  // Convert ApiResponse to JSON Map
  static Map<String, dynamic> toJsonMap(ParseValidateGo response) {
    return response.toJson();
  }

  // Extract GoModel from ApiResponse for compatibility with existing code
  static GoModel toGoModel(ParseValidateGo response) {
    if (response.parsedData?.go == null) {
      throw Exception('No GO data found in response');
    }

    final goData = response.parsedData!.go!;

    return GoModel(
      globalObjectives: goData.globalObjectives,
      processOwnership: goData.processOwnership,
      triggerDefinition: goData.triggerDefinition,
      localObjectivesList: goData.localObjectivesList?.cast<LocalObjectivesList>(),
      pathwayDefinitions: goData.pathwayDefinitions?.cast<PathwayDefinition>(),
      performanceMetadata: _convertToPerformanceMetadataClass(goData.performanceMetadata),
      processMiningSchema: goData.processMiningSchema,
      performanceDiscoveryMetrics: _convertToPerformanceMetadataClass2(goData.performanceDiscoveryMetrics),
      conformanceAnalytics: _convertToGoModelConformanceAnalytics(goData.conformanceAnalytics),
      advancedProcessIntelligence: _convertToGoModelAdvancedProcessIntelligence(goData.advancedProcessIntelligence),
      rollbackPathways: goData.rollbackPathways?.cast<RollbackPathway>(),
      validationRules: _convertToValidationRulesClass(goData.validationRules),
      dataConstraints: goData.dataConstraints?.cast<DataConstraint>(),
      processFlow: goData.processFlow?.cast<ProcessFlow>(),
    );
  }

  // Helper conversion methods
  static PerformanceMetadataClass? _convertToPerformanceMetadataClass(EnhancedPerformanceMetadata? enhanced) {
    if (enhanced == null) return null;
    return PerformanceMetadataClass(
      id: enhanced.id,
      goId: enhanced.goId,
      naturalLanguage: enhanced.naturalLanguage,
      metadataData: enhanced.metadataData,
    );
  }

  static PerformanceMetadataClass? _convertToPerformanceMetadataClass2(EnhancedPerformanceDiscoveryMetrics? enhanced) {
    if (enhanced == null) return null;
    return PerformanceMetadataClass(
      id: enhanced.id,
      goId: enhanced.goId,
      naturalLanguage: enhanced.naturalLanguage,
    );
  }

  static GoModelConformanceAnalytics? _convertToGoModelConformanceAnalytics(EnhancedConformanceAnalytics? enhanced) {
    if (enhanced == null) return null;
    return GoModelConformanceAnalytics(
      id: enhanced.id,
      goId: enhanced.goId,
      naturalLanguage: enhanced.naturalLanguage,
      complianceRate: enhanced.complianceRate,
    );
  }

  static GoModelAdvancedProcessIntelligence? _convertToGoModelAdvancedProcessIntelligence(EnhancedAdvancedProcessIntelligence? enhanced) {
    if (enhanced == null) return null;
    return GoModelAdvancedProcessIntelligence(
      id: enhanced.id,
      goId: enhanced.goId,
      naturalLanguage: enhanced.naturalLanguage,
      processHealthScore: enhanced.processHealthScore,
      optimizationInsights: enhanced.optimizationInsights,
    );
  }

  static PerformanceMetadataClass? _convertToValidationRulesClass(EnhancedValidationRules? enhanced) {
    if (enhanced == null) return null;
    return PerformanceMetadataClass(
      id: enhanced.id,
      goId: enhanced.goId,
      naturalLanguage: enhanced.naturalLanguage,
      rules: enhanced.rules?.cast<Rule>(),
    );
  }
}

// Usage Example
