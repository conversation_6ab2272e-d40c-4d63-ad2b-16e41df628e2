import 'package:flutter/material.dart';
import 'package:nsl/screens/web/static_flow/get_all_my_library_model.dart';

class CreationProvider extends ChangeNotifier {
  int _currentMiddleScreen = 1;
  // 0 - roles 1- objects 2-GO
  bool _isGoMyLibraryClicked = false;

  // Role auto-population data
  RolesPostgre? _prePopulatedRole;
  List<RolesPostgre> _allRoles = [];

  int get currentMiddleScreen => _currentMiddleScreen;
  bool get isGoMyLibraryClicked => _isGoMyLibraryClicked;
  RolesPostgre? get prePopulatedRole => _prePopulatedRole;
  List<RolesPostgre> get allRoles => _allRoles;

  set currentMiddleScreen(value) {
    _currentMiddleScreen = value;
    notifyListeners();
  }

  set isGoMyLibraryClicked(value) {
    _isGoMyLibraryClicked = value;
    notifyListeners();
  }

  /// Set role data for auto-population in role creation screen
  void setRoleForCreation(RolesPostgre? role, List<RolesPostgre> allRoles) {
    _prePopulatedRole = role;
    _allRoles = allRoles;
    notifyListeners();
  }

  /// Clear role data
  void clearRoleData() {
    _prePopulatedRole = null;
    _allRoles = [];
    notifyListeners();
  }
}
