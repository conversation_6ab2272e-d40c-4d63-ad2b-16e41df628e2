import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';

class CustomDropdownWidget extends StatelessWidget {
  CustomDropdownWidget(
      {super.key,
      required this.label,
      required this.list,
      required this.onChanged,
      required this.value,
      this.closeOnTapOutside = true,
      this.customLabelWidget});

  final String label;
  final List list;
  final dynamic value;
  final Function(dynamic) onChanged;
  final bool closeOnTapOutside;
  final Widget? customLabelWidget;

  @override
  Widget build(BuildContext context) {
    return _buildDropdown(
      context,
      label,
      list,
      value,
      onChanged,
    );
  }

  Widget _buildDropdown(
    BuildContext context,
    String label,
    List list,
    dynamic value,
    Function(dynamic) onChanged,
  ) {
    final layerLink = LayerLink();
    final key = GlobalKey();
    OverlayEntry? overlayEntry;

    void removeOverlay() {
      overlayEntry?.remove();
      overlayEntry = null;
    }

    void showOverlay() {
      final RenderBox renderBox =
          key.currentContext!.findRenderObject() as RenderBox;
      final Size size = renderBox.size;
      final Offset offset = renderBox.localToGlobal(Offset.zero);

      overlayEntry = OverlayEntry(
        builder: (context) => Stack(
          children: [
            if (closeOnTapOutside)
              // Fullscreen GestureDetector to detect taps outside
              Positioned.fill(
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    removeOverlay();
                  },
                ),
              ),
            // The dropdown overlay itself
            Positioned(
              left: offset.dx,
              top: offset.dy + size.height,
              width: size.width,
              child: CompositedTransformFollower(
                link: layerLink,
                offset: Offset(0, size.height + 5),
                child: _buildOverlayOptions(context, list, (selected) {
                  onChanged(selected);
                  removeOverlay();
                }),
              ),
            ),
          ],
        ),
      );

      Overlay.of(context).insert(overlayEntry!);
    }

    return CompositedTransformTarget(
      link: layerLink,
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: InkWell(
          key: key,
          onTap: () {
            if (overlayEntry == null) {
              showOverlay();
            } else {
              removeOverlay();
            }
          },
          child: Container(
            height: 35,
            width: 150,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(2),
              color: Colors.white,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                customLabelWidget ??
                    Text(
                      value ?? " $label",
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s10,
                        fontWeight: FontWeight.w400,
                        color:
                            value == null ? Colors.grey.shade600 : Colors.black,
                        fontFamily: FontManager.fontFamilyTiemposText,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                Icon(Icons.keyboard_arrow_down,
                    color: Colors.grey.shade600, size: 12),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOverlayOptions(
      BuildContext context, List list, Function(dynamic) onItemSelected) {
    return Material(
      elevation: 0,
      borderRadius: BorderRadius.circular(4),
      child: Container(
        constraints: BoxConstraints(maxHeight: 200),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(2),
          color: Colors.white,
        ),
        child: ListView(
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          children: list.map((item) {
            return InkWell(
              onTap: () {
                onItemSelected(item);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                child: Text(
                  item,
                  style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12, color: Colors.black),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
